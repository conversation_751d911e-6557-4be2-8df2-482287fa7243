# Image Converter - Chuy<PERSON><PERSON> đổ<PERSON> sang WebP với dịch tên file

Ứng dụng Python desktop để chuyển đổi hình ảnh JPG sang WebP và dịch tên file từ tiếng Việt sang tiếng Anh theo chuẩn ngành Casino.

## Tính năng chính

- **Chuyển đổi định dạng**: JPG → WebP với chất lượng cao
- **Dịch tên file**: Tiếng Việt → Tiếng Anh sử dụng Gemini AI
- **Chuẩn hóa tên file**: Lowercase, dấu gạch ngang thay thế khoảng trắng
- **Xử lý batch**: <PERSON><PERSON><PERSON><PERSON> thư mục và file cùng lúc
- **Giao diện thân thiện**: GUI với thanh tiến trình và log console
- **Xử lý song song**: Threading để tăng hiệu suất

## Y<PERSON>u cầu hệ thống

- Windows 10/11
- Python 3.8+
- <PERSON>ết nối Internet (cho Gemini API)

## Cài đặt

1. **Clone hoặc tải về dự án**
```bash
git clone <repository-url>
cd image-viet-to-anh
```

2. **Cài đặt dependencies**
```bash
pip install -r requirements.txt
```

3. **Cấu hình API Key**
   - Truy cập [Google AI Studio](https://aistudio.google.com/) để lấy API key
   - Mở file `config.py`
   - Thay đổi `YOUR_GEMINI_API_KEY_HERE` thành API key thực của bạn
   - Xem file `API_KEY_SETUP.md` để biết hướng dẫn chi tiết

## Sử dụng

1. **Khởi chạy ứng dụng**
```bash
python main.py
```

2. **Chọn thư mục**
   - Nhấn "Thêm thư mục" để chọn thư mục chứa hình ảnh JPG
   - Có thể chọn nhiều thư mục

3. **Cấu hình tùy chọn**
   - Chọn "Giữ lại file gốc" nếu muốn giữ file JPG ban đầu

4. **Bắt đầu xử lý**
   - Nhấn "Bắt đầu xử lý"
   - Theo dõi tiến trình qua thanh progress và log console

## Demo

Để test ứng dụng nhanh:

1. **Tạo hình ảnh demo**
```bash
python create_demo_images.py
```

2. **Chạy ứng dụng và chọn thư mục `demo_images`**

3. **Xem kết quả**: File WebP sẽ được tạo với tên đã dịch và chuẩn hóa

## Cấu trúc dự án

```
image-viet-to-anh/
├── main.py                 # File chính
├── config.py              # Cấu hình
├── main_gui.py            # Giao diện GUI
├── batch_processor.py     # Xử lý batch
├── image_converter.py     # Chuyển đổi hình ảnh
├── gemini_translator.py   # Tích hợp Gemini API
├── file_utils.py          # Tiện ích xử lý file
├── requirements.txt       # Dependencies
├── README.md             # Hướng dẫn
└── logs/                 # Thư mục log
```

## Cấu hình

Trong file `config.py` bạn có thể tùy chỉnh:

- `GEMINI_API_KEY`: API key của Gemini
- `WEBP_QUALITY`: Chất lượng WebP (0-100)
- `BATCH_SIZE`: Số file xử lý đồng thời
- `MAX_RETRIES`: Số lần thử lại khi API lỗi

## Xử lý lỗi

- **API lỗi**: Tự động retry với delay
- **Quota exceeded**: Sử dụng cache và fallback mode
- **File không hợp lệ**: Bỏ qua và ghi log
- **Tên file không dịch được**: Sử dụng tên gốc
- **Lỗi chuyển đổi**: Ghi log chi tiết

## Tính năng nâng cao

### Cache Translation
- Tự động lưu kết quả dịch trong `translation_cache.json`
- Tránh dịch lại những tên file giống nhau
- Giảm đáng kể API calls

### Xử lý tên file đã chuẩn hóa
- Phát hiện tên file tiếng Việt không dấu (như `tong-quan-ve-slots`)
- Tự động phục hồi dấu tiếng Việt trước khi dịch
- Dịch chính xác hơn với ngữ cảnh đầy đủ

### Quota Management
- Xem file `QUOTA_MANAGEMENT.md` để biết cách xử lý quota exceeded
- Tự động giảm tốc độ khi gặp rate limit
- Fallback mode khi API không khả dụng

## Log

- Log được lưu trong thư mục `logs/app.log`
- Hiển thị real-time trong GUI
- Bao gồm thông tin chi tiết về quá trình xử lý

## Hỗ trợ

Nếu gặp vấn đề, vui lòng kiểm tra:

1. API key Gemini có hợp lệ không
2. Kết nối Internet
3. File log để xem lỗi chi tiết
4. Quyền ghi file trong thư mục đích

## License

MIT License
