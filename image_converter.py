"""
Module xử lý chuyển đổi hình ảnh từ JPG sang WebP
"""

import os
from PIL import Image
import logging
from config import WEBP_QUALITY, SUPPORTED_FORMATS

class ImageConverter:
    def __init__(self, quality=WEBP_QUALITY):
        """
        Khởi tạo ImageConverter
        
        Args:
            quality (int): Chất lượng WebP (0-100)
        """
        self.quality = quality
        self.logger = logging.getLogger(__name__)
    
    def is_supported_format(self, file_path):
        """
        Kiểm tra xem file có phải định dạng được hỗ trợ không
        
        Args:
            file_path (str): Đường dẫn file
            
        Returns:
            bool: True nếu được hỗ trợ
        """
        _, ext = os.path.splitext(file_path)
        return ext.lower() in [fmt.lower() for fmt in SUPPORTED_FORMATS]
    
    def convert_to_webp(self, input_path, output_path):
        """
        Chuyển đổi hình ảnh sang WebP
        
        Args:
            input_path (str): Đường dẫn file đầu vào
            output_path (str): Đường dẫn file đầu ra
            
        Returns:
            bool: <PERSON> nếu thành công
        """
        try:
            # Mở và chuyển đổi hình ảnh
            with Image.open(input_path) as img:
                # Chuyển sang RGB nếu cần (để tránh lỗi với RGBA)
                if img.mode in ('RGBA', 'LA', 'P'):
                    img = img.convert('RGB')
                
                # Lưu dưới định dạng WebP
                img.save(output_path, 'WebP', quality=self.quality, optimize=True)
                
            self.logger.info(f"Đã chuyển đổi: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Lỗi chuyển đổi {input_path}: {str(e)}")
            return False
    
    def get_output_path(self, input_path, new_filename):
        """
        Tạo đường dẫn file đầu ra
        
        Args:
            input_path (str): Đường dẫn file gốc
            new_filename (str): Tên file mới (không có extension)
            
        Returns:
            str: Đường dẫn file WebP đầu ra
        """
        directory = os.path.dirname(input_path)
        return os.path.join(directory, f"{new_filename}.webp")
    
    def convert_with_new_name(self, input_path, new_filename):
        """
        Chuyển đổi hình ảnh với tên file mới
        
        Args:
            input_path (str): Đường dẫn file đầu vào
            new_filename (str): Tên file mới (không có extension)
            
        Returns:
            tuple: (success: bool, output_path: str)
        """
        if not self.is_supported_format(input_path):
            self.logger.warning(f"Định dạng không được hỗ trợ: {input_path}")
            return False, None
        
        if not os.path.exists(input_path):
            self.logger.error(f"File không tồn tại: {input_path}")
            return False, None
        
        output_path = self.get_output_path(input_path, new_filename)
        success = self.convert_to_webp(input_path, output_path)
        
        return success, output_path if success else None
