"""
Script test để kiểm tra các chức năng cơ bản của ứng dụng
"""

import os
import logging
from file_utils import FileNameNormalizer
from gemini_translator import GeminiTranslator
from image_converter import ImageConverter

def test_file_normalizer():
    """
    Test module chuẩn hóa tên file
    """
    print("=== Test FileNameNormalizer ===")
    normalizer = FileNameNormalizer()
    
    test_cases = [
        "Hình ảnh đẹp",
        "Ảnh casino số 1",
        "Bài bạc & game thú vị",
        "Slot machine - jackpot!!!",
        "Poker_game_2024",
        "Rồng hổ phát tài"
    ]
    
    for test_case in test_cases:
        normalized = normalizer.normalize_filename(test_case)
        print(f"'{test_case}' -> '{normalized}'")
    
    print("✓ FileNameNormalizer test completed\n")

def test_gemini_translator():
    """
    Test Gemini translator
    """
    print("=== Test GeminiTranslator ===")
    
    try:
        translator = GeminiTranslator()
        
        # Test connection
        if translator.test_connection():
            print("✓ Gemini API connection successful")
            
            # Test translation
            test_names = [
                "Hình ảnh casino",
                "Bài bạc",
                "Slot machine"
            ]
            
            for name in test_names:
                translated = translator.translate_filename(name)
                print(f"'{name}' -> '{translated}'")
                
        else:
            print("✗ Gemini API connection failed")
            
    except Exception as e:
        print(f"✗ Gemini translator error: {str(e)}")
    
    print("✓ GeminiTranslator test completed\n")

def test_image_converter():
    """
    Test image converter
    """
    print("=== Test ImageConverter ===")
    
    converter = ImageConverter()
    
    # Test supported formats
    test_files = [
        "test.jpg",
        "test.jpeg",
        "test.JPG",
        "test.png",
        "test.gif"
    ]
    
    for file in test_files:
        is_supported = converter.is_supported_format(file)
        print(f"'{file}' supported: {is_supported}")
    
    print("✓ ImageConverter test completed\n")

def main():
    """
    Chạy tất cả tests
    """
    print("Bắt đầu test ứng dụng Image Converter...\n")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    # Run tests
    test_file_normalizer()
    test_image_converter()
    test_gemini_translator()
    
    print("=== Test Summary ===")
    print("Tất cả tests đã hoàn thành!")
    print("Nếu không có lỗi, ứng dụng sẵn sàng sử dụng.")

if __name__ == "__main__":
    main()
