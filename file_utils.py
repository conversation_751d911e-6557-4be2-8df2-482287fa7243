"""
Module tiện ích xử lý tên file
"""

import re
import os
import unicodedata
import logging

class FileNameNormalizer:
    def __init__(self):
        """
        Khởi tạo FileNameNormalizer
        """
        self.logger = logging.getLogger(__name__)
    
    def normalize_filename(self, filename):
        """
        Chuẩn hóa tên file theo format casino
        - Chuyển về lowercase
        - Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
        - Loại bỏ dấu tiếng Việt
        - Loại bỏ ký tự không hợp lệ
        
        Args:
            filename (str): Tên file gốc
            
        Returns:
            str: Tên file đã chuẩn hóa
        """
        if not filename:
            return "unnamed"
        
        # Loại bỏ dấu tiếng Việt và chuyển về ASCII
        normalized = self._remove_vietnamese_accents(filename)
        
        # Chuyển về lowercase
        normalized = normalized.lower()
        
        # Thay thế khoảng trắng và ký tự đặc biệt bằng dấu gạch ngang
        normalized = re.sub(r'[^\w\s-]', '', normalized)  # Loại bỏ ký tự đặc biệt
        normalized = re.sub(r'[\s_]+', '-', normalized)   # Thay khoảng trắng và _ bằng -
        normalized = re.sub(r'-+', '-', normalized)       # Gộp nhiều dấu - thành một
        
        # Loại bỏ dấu - ở đầu và cuối
        normalized = normalized.strip('-')
        
        # Đảm bảo tên file không rỗng
        if not normalized:
            normalized = "unnamed"
        
        # Giới hạn độ dài tên file
        if len(normalized) > 100:
            normalized = normalized[:100].rstrip('-')
        
        self.logger.debug(f"Chuẩn hóa: '{filename}' -> '{normalized}'")
        return normalized
    
    def _remove_vietnamese_accents(self, text):
        """
        Loại bỏ dấu tiếng Việt
        
        Args:
            text (str): Văn bản có dấu
            
        Returns:
            str: Văn bản không dấu
        """
        # Mapping các ký tự tiếng Việt đặc biệt
        vietnamese_map = {
            'à': 'a', 'á': 'a', 'ạ': 'a', 'ả': 'a', 'ã': 'a',
            'â': 'a', 'ầ': 'a', 'ấ': 'a', 'ậ': 'a', 'ẩ': 'a', 'ẫ': 'a',
            'ă': 'a', 'ằ': 'a', 'ắ': 'a', 'ặ': 'a', 'ẳ': 'a', 'ẵ': 'a',
            'è': 'e', 'é': 'e', 'ẹ': 'e', 'ẻ': 'e', 'ẽ': 'e',
            'ê': 'e', 'ề': 'e', 'ế': 'e', 'ệ': 'e', 'ể': 'e', 'ễ': 'e',
            'ì': 'i', 'í': 'i', 'ị': 'i', 'ỉ': 'i', 'ĩ': 'i',
            'ò': 'o', 'ó': 'o', 'ọ': 'o', 'ỏ': 'o', 'õ': 'o',
            'ô': 'o', 'ồ': 'o', 'ố': 'o', 'ộ': 'o', 'ổ': 'o', 'ỗ': 'o',
            'ơ': 'o', 'ờ': 'o', 'ớ': 'o', 'ợ': 'o', 'ở': 'o', 'ỡ': 'o',
            'ù': 'u', 'ú': 'u', 'ụ': 'u', 'ủ': 'u', 'ũ': 'u',
            'ư': 'u', 'ừ': 'u', 'ứ': 'u', 'ự': 'u', 'ử': 'u', 'ữ': 'u',
            'ỳ': 'y', 'ý': 'y', 'ỵ': 'y', 'ỷ': 'y', 'ỹ': 'y',
            'đ': 'd'
        }
        
        # Thay thế ký tự tiếng Việt
        for vietnamese, english in vietnamese_map.items():
            text = text.replace(vietnamese, english)
            text = text.replace(vietnamese.upper(), english.upper())
        
        # Sử dụng unicodedata để loại bỏ dấu còn lại
        text = unicodedata.normalize('NFD', text)
        text = ''.join(char for char in text if unicodedata.category(char) != 'Mn')
        
        return text
    
    def validate_filename(self, filename):
        """
        Kiểm tra tính hợp lệ của tên file
        
        Args:
            filename (str): Tên file cần kiểm tra
            
        Returns:
            bool: True nếu hợp lệ
        """
        if not filename:
            return False
        
        # Kiểm tra ký tự không hợp lệ trong Windows
        invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '\\', '/']
        for char in invalid_chars:
            if char in filename:
                return False
        
        # Kiểm tra tên file dành riêng trong Windows
        reserved_names = [
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        ]
        
        if filename.upper() in reserved_names:
            return False
        
        return True
    
    def get_safe_filename(self, original_name, fallback_name="unnamed"):
        """
        Tạo tên file an toàn
        
        Args:
            original_name (str): Tên file gốc
            fallback_name (str): Tên dự phòng nếu không thể chuẩn hóa
            
        Returns:
            str: Tên file an toàn
        """
        normalized = self.normalize_filename(original_name)
        
        if self.validate_filename(normalized):
            return normalized
        else:
            self.logger.warning(f"Tên file không hợp lệ: '{normalized}', sử dụng tên dự phòng")
            return fallback_name
