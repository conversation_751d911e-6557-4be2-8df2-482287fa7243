"""
Module xử lý batch processing cho nhiều file hình ảnh
"""

import os
import threading
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
from image_converter import ImageConverter
from gemini_translator import GeminiTranslator
from file_utils import FileNameNormalizer
from config import BATCH_SIZE, SUPPORTED_FORMATS

class BatchProcessor:
    def __init__(self, folders, keep_original=True, progress_callback=None, log_callback=None):
        """
        Khởi tạo BatchProcessor
        
        Args:
            folders (list): <PERSON>h sách thư mục cần xử lý
            keep_original (bool): Giữ lại file gốc hay không
            progress_callback (callable): Callback để cập nhật tiến trình
            log_callback (callable): Callback để ghi log
        """
        self.folders = folders
        self.keep_original = keep_original
        self.progress_callback = progress_callback
        self.log_callback = log_callback
        
        self.logger = logging.getLogger(__name__)
        self.image_converter = ImageConverter()
        self.translator = GeminiTranslator()
        self.normalizer = FileNameNormalizer()
        
        self.total_files = 0
        self.processed_files = 0
        self.successful_files = 0
        self.failed_files = 0
        
        self.stop_requested = False
    
    def find_image_files(self):
        """
        Tìm tất cả file hình ảnh trong các thư mục
        
        Returns:
            list: Danh sách đường dẫn file hình ảnh
        """
        image_files = []
        
        for folder in self.folders:
            if not os.path.exists(folder):
                self.logger.warning(f"Thư mục không tồn tại: {folder}")
                continue
            
            self.logger.info(f"Đang quét thư mục: {folder}")
            
            for root, dirs, files in os.walk(folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    
                    if self.image_converter.is_supported_format(file_path):
                        image_files.append(file_path)
        
        self.logger.info(f"Tìm thấy {len(image_files)} file hình ảnh")
        return image_files
    
    def process_single_file(self, file_path):
        """
        Xử lý một file hình ảnh
        
        Args:
            file_path (str): Đường dẫn file
            
        Returns:
            bool: True nếu thành công
        """
        try:
            if self.stop_requested:
                return False
            
            # Lấy tên file gốc (không có extension)
            original_name = os.path.splitext(os.path.basename(file_path))[0]
            
            self.logger.info(f"Đang xử lý: {original_name}")
            
            # Dịch tên file
            translated_name = self.translator.translate_filename(original_name)
            if not translated_name:
                translated_name = original_name
                self.logger.warning(f"Không thể dịch, sử dụng tên gốc: {original_name}")
            
            # Chuẩn hóa tên file
            normalized_name = self.normalizer.get_safe_filename(translated_name)
            
            # Chuyển đổi hình ảnh
            success, output_path = self.image_converter.convert_with_new_name(
                file_path, normalized_name
            )
            
            if success:
                self.logger.info(f"Thành công: {original_name} -> {normalized_name}.webp")
                
                # Xóa file gốc nếu được yêu cầu
                if not self.keep_original:
                    try:
                        os.remove(file_path)
                        self.logger.info(f"Đã xóa file gốc: {file_path}")
                    except Exception as e:
                        self.logger.warning(f"Không thể xóa file gốc {file_path}: {str(e)}")
                
                return True
            else:
                self.logger.error(f"Thất bại: {original_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Lỗi xử lý file {file_path}: {str(e)}")
            return False
    
    def update_progress(self, message=""):
        """
        Cập nhật tiến trình
        """
        if self.progress_callback:
            self.progress_callback(self.processed_files, self.total_files, message)
    
    def process(self):
        """
        Xử lý tất cả file hình ảnh
        """
        try:
            # Test kết nối Gemini API
            self.logger.info("Đang kiểm tra kết nối Gemini API...")
            if not self.translator.test_connection():
                self.logger.error("Không thể kết nối Gemini API!")
                return
            
            self.logger.info("Kết nối Gemini API thành công")
            
            # Tìm tất cả file hình ảnh
            image_files = self.find_image_files()
            
            if not image_files:
                self.logger.warning("Không tìm thấy file hình ảnh nào!")
                return
            
            self.total_files = len(image_files)
            self.processed_files = 0
            self.successful_files = 0
            self.failed_files = 0
            
            self.logger.info(f"Bắt đầu xử lý {self.total_files} file...")
            self.update_progress("Đang xử lý...")
            
            # Xử lý file với ThreadPoolExecutor
            with ThreadPoolExecutor(max_workers=BATCH_SIZE) as executor:
                # Submit tất cả tasks
                future_to_file = {
                    executor.submit(self.process_single_file, file_path): file_path
                    for file_path in image_files
                }
                
                # Xử lý kết quả
                for future in as_completed(future_to_file):
                    if self.stop_requested:
                        break
                    
                    file_path = future_to_file[future]
                    
                    try:
                        success = future.result()
                        if success:
                            self.successful_files += 1
                        else:
                            self.failed_files += 1
                    except Exception as e:
                        self.logger.error(f"Lỗi xử lý {file_path}: {str(e)}")
                        self.failed_files += 1
                    
                    self.processed_files += 1
                    
                    # Cập nhật tiến trình
                    progress_msg = f"Đã xử lý {self.processed_files}/{self.total_files} file"
                    self.update_progress(progress_msg)
            
            # Báo cáo kết quả
            self.logger.info(f"Hoàn thành! Thành công: {self.successful_files}, Thất bại: {self.failed_files}")
            
        except Exception as e:
            self.logger.error(f"Lỗi trong quá trình xử lý: {str(e)}")
        finally:
            self.update_progress("Hoàn thành")
    
    def stop(self):
        """
        Dừng xử lý
        """
        self.stop_requested = True
        self.logger.info("Đã yêu cầu dừng xử lý")
