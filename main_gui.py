"""
Giao diện GUI chính cho ứng dụng Image Converter
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import logging
import os
from config import WINDOW_WIDTH, WINDOW_HEIGHT, LOG_MAX_LINES

class MainGUI:
    def __init__(self, root):
        """
        Khởi tạo giao diện chính
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.selected_folders = []
        self.is_processing = False
        self.keep_original = tk.BooleanVar(value=True)
        
        self.setup_window()
        self.setup_logging()
        self.create_widgets()
        
    def setup_window(self):
        """
        Thiết lập cửa sổ ch<PERSON>h
        """
        self.root.title("Image Converter - Chuyển đổi JPG sang WebP")
        self.root.geometry(f"{WINDOW_WIDTH}x{WINDOW_HEIGHT}")
        self.root.resizable(True, True)
        
        # Icon (nếu có)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def setup_logging(self):
        """
        <PERSON>hi<PERSON><PERSON> lập logging để hiển thị trong GUI
        """
        self.log_handler = GUILogHandler(self)
        self.log_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        self.log_handler.setFormatter(formatter)
        
        # Thêm handler vào root logger
        logging.getLogger().addHandler(self.log_handler)
        logging.getLogger().setLevel(logging.INFO)
    
    def create_widgets(self):
        """
        Tạo các widget cho giao diện
        """
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # Folder selection section
        self.create_folder_section(main_frame)
        
        # Options section
        self.create_options_section(main_frame)
        
        # Control buttons
        self.create_control_section(main_frame)
        
        # Progress bar
        self.create_progress_section(main_frame)
        
        # Log console
        self.create_log_section(main_frame)
    
    def create_folder_section(self, parent):
        """
        Tạo phần chọn thư mục
        """
        # Label
        ttk.Label(parent, text="Chọn thư mục chứa hình ảnh:").grid(
            row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 5)
        )
        
        # Folder list
        self.folder_listbox = tk.Listbox(parent, height=4)
        self.folder_listbox.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        
        # Scrollbar for listbox
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.folder_listbox.yview)
        scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S), pady=(0, 5))
        self.folder_listbox.configure(yscrollcommand=scrollbar.set)
        
        # Buttons
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=2, column=0, columnspan=3, sticky=tk.W, pady=(0, 10))
        
        ttk.Button(button_frame, text="Thêm thư mục", command=self.add_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Xóa thư mục", command=self.remove_folder).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="Xóa tất cả", command=self.clear_folders).pack(side=tk.LEFT)
    
    def create_options_section(self, parent):
        """
        Tạo phần tùy chọn
        """
        options_frame = ttk.LabelFrame(parent, text="Tùy chọn", padding="5")
        options_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Checkbutton(
            options_frame, 
            text="Giữ lại file gốc", 
            variable=self.keep_original
        ).pack(anchor=tk.W)
    
    def create_control_section(self, parent):
        """
        Tạo phần nút điều khiển
        """
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=4, column=0, columnspan=3, pady=(0, 10))
        
        self.start_button = ttk.Button(
            control_frame, 
            text="Bắt đầu xử lý", 
            command=self.start_processing,
            style="Accent.TButton"
        )
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(
            control_frame, 
            text="Dừng", 
            command=self.stop_processing,
            state=tk.DISABLED
        )
        self.stop_button.pack(side=tk.LEFT)
    
    def create_progress_section(self, parent):
        """
        Tạo thanh tiến trình
        """
        progress_frame = ttk.Frame(parent)
        progress_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_frame, 
            variable=self.progress_var, 
            maximum=100
        )
        self.progress_bar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
        
        self.progress_label = ttk.Label(progress_frame, text="Sẵn sàng")
        self.progress_label.grid(row=1, column=0, sticky=tk.W)
    
    def create_log_section(self, parent):
        """
        Tạo phần log console
        """
        log_frame = ttk.LabelFrame(parent, text="Log", padding="5")
        log_frame.grid(row=6, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(
            log_frame, 
            height=10, 
            state=tk.DISABLED,
            wrap=tk.WORD
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def add_folder(self):
        """
        Thêm thư mục vào danh sách
        """
        folder = filedialog.askdirectory(title="Chọn thư mục chứa hình ảnh")
        if folder and folder not in self.selected_folders:
            self.selected_folders.append(folder)
            self.folder_listbox.insert(tk.END, folder)
            logging.info(f"Đã thêm thư mục: {folder}")
    
    def remove_folder(self):
        """
        Xóa thư mục được chọn
        """
        selection = self.folder_listbox.curselection()
        if selection:
            index = selection[0]
            folder = self.selected_folders.pop(index)
            self.folder_listbox.delete(index)
            logging.info(f"Đã xóa thư mục: {folder}")
    
    def clear_folders(self):
        """
        Xóa tất cả thư mục
        """
        self.selected_folders.clear()
        self.folder_listbox.delete(0, tk.END)
        logging.info("Đã xóa tất cả thư mục")
    
    def start_processing(self):
        """
        Bắt đầu xử lý
        """
        if not self.selected_folders:
            messagebox.showwarning("Cảnh báo", "Vui lòng chọn ít nhất một thư mục!")
            return
        
        self.is_processing = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.progress_label.config(text="Đang khởi tạo...")
        
        # Chạy xử lý trong thread riêng
        self.processing_thread = threading.Thread(target=self._process_images)
        self.processing_thread.daemon = True
        self.processing_thread.start()
    
    def stop_processing(self):
        """
        Dừng xử lý
        """
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_label.config(text="Đã dừng")
        logging.info("Đã dừng xử lý")
    
    def _process_images(self):
        """
        Xử lý hình ảnh (chạy trong thread riêng)
        """
        try:
            # Import batch processor
            from batch_processor import BatchProcessor
            
            processor = BatchProcessor(
                folders=self.selected_folders,
                keep_original=self.keep_original.get(),
                progress_callback=self.update_progress,
                log_callback=self.log_message
            )
            
            processor.process()
            
        except Exception as e:
            logging.error(f"Lỗi xử lý: {str(e)}")
        finally:
            self.root.after(0, self._processing_finished)
    
    def _processing_finished(self):
        """
        Hoàn thành xử lý
        """
        self.is_processing = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_label.config(text="Hoàn thành")
        logging.info("Đã hoàn thành xử lý")
    
    def update_progress(self, current, total, message=""):
        """
        Cập nhật thanh tiến trình
        """
        if total > 0:
            progress = (current / total) * 100
            self.root.after(0, lambda: self.progress_var.set(progress))
        
        if message:
            self.root.after(0, lambda: self.progress_label.config(text=message))
    
    def log_message(self, message):
        """
        Thêm message vào log
        """
        self.root.after(0, lambda: self._append_log(message))
    
    def _append_log(self, message):
        """
        Thêm message vào log text widget
        """
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, message + "\n")
        
        # Giới hạn số dòng log
        lines = int(self.log_text.index('end-1c').split('.')[0])
        if lines > LOG_MAX_LINES:
            self.log_text.delete('1.0', f'{lines - LOG_MAX_LINES}.0')
        
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)


class GUILogHandler(logging.Handler):
    """
    Custom log handler để hiển thị log trong GUI
    """
    def __init__(self, gui):
        super().__init__()
        self.gui = gui
    
    def emit(self, record):
        msg = self.format(record)
        self.gui.log_message(msg)
