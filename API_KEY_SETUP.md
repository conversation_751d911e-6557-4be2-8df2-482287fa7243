# Hướng dẫn cài đặt Gemini API Key

## Bước 1: Lấy API Key từ Google AI Studio

1. <PERSON><PERSON><PERSON> cập [Google AI Studio](https://aistudio.google.com/)
2. Đăng nhập bằng tài khoản Google
3. Nhấn vào "Get API Key" hoặc "Create API Key"
4. Tạo API key mới
5. Copy API key (dạng: AIza...)

## Bước 2: Cấu hình API Key

1. Mở file `config.py` trong thư mục ứng dụng
2. Tìm dòng:
   ```python
   GEMINI_API_KEY = "AIzaSyDiHgXXBhIW6KycYK4AwWluLNjYx-pzG-0"
   ```
3. Thay thế bằng API key của bạn:
   ```python
   GEMINI_API_KEY = "YOUR_ACTUAL_API_KEY_HERE"
   ```
4. Lưu file

## Bước 3: Kiểm tra kết nối

Chạy lệnh test:
```bash
python test_app.py
```

Nếu thấy "✓ Gemini API connection successful" thì đã thành công.

## Lưu ý quan trọng

- **<PERSON><PERSON><PERSON> mật**: Không chia sẻ API key với người khác
- **Quota**: API có giới hạn sử dụng miễn phí
- **Billing**: Có thể cần thiết lập billing account cho sử dụng nhiều

## Xử lý lỗi thường gặp

### API_KEY_INVALID
- Kiểm tra lại API key có đúng không
- Đảm bảo API key bắt đầu bằng "AIza"

### QUOTA_EXCEEDED  
- Đã vượt quá giới hạn miễn phí
- Cần thiết lập billing account

### Network Error
- Kiểm tra kết nối Internet
- Có thể cần VPN nếu bị chặn

## Fallback Mode

Nếu không thể sử dụng Gemini API, ứng dụng sẽ:
- Sử dụng tên file gốc
- Vẫn chuẩn hóa tên file (loại bỏ dấu, lowercase)
- Ghi log cảnh báo
