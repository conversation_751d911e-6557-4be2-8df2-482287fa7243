"""
File chính để khởi chạy ứng dụng Image Converter
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sys
import os
from main_gui import MainGUI

def setup_logging():
    """
    Thiết lập logging cho ứng dụng
    """
    # <PERSON><PERSON><PERSON> thư mục logs nếu chưa có
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # Cấu hình logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('logs/app.log', encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ]
    )

def check_dependencies():
    """
    Kiểm tra các dependencies cần thiết
    
    Returns:
        bool: True nếu tất cả dependencies đều có
    """
    missing_deps = []
    
    try:
        import PIL
    except ImportError:
        missing_deps.append("Pillow")
    
    try:
        import google.generativeai
    except ImportError:
        missing_deps.append("google-generativeai")
    
    if missing_deps:
        error_msg = f"Thiếu các thư viện sau:\n{', '.join(missing_deps)}\n\n"
        error_msg += "Vui lòng cài đặt bằng lệnh:\n"
        error_msg += f"pip install {' '.join(missing_deps)}"
        
        messagebox.showerror("Lỗi Dependencies", error_msg)
        return False
    
    return True

def check_api_key():
    """
    Kiểm tra API key Gemini

    Returns:
        bool: True nếu API key hợp lệ
    """
    from config import GEMINI_API_KEY

    if not GEMINI_API_KEY or GEMINI_API_KEY in ["YOUR_API_KEY_HERE", "YOUR_GEMINI_API_KEY_HERE"]:
        error_msg = """Vui lòng cấu hình GEMINI_API_KEY trong file config.py

Hướng dẫn:
1. Truy cập https://aistudio.google.com/
2. Tạo API key mới
3. Thay thế YOUR_GEMINI_API_KEY_HERE trong config.py
4. Xem file API_KEY_SETUP.md để biết chi tiết"""
        messagebox.showerror("Cần cấu hình API Key", error_msg)
        return False

    return True

def main():
    """
    Hàm main để khởi chạy ứng dụng
    """
    # Thiết lập logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    logger.info("Đang khởi động Image Converter...")
    
    # Kiểm tra dependencies
    if not check_dependencies():
        return
    
    # Kiểm tra API key
    if not check_api_key():
        return
    
    try:
        # Tạo root window
        root = tk.Tk()
        
        # Thiết lập style cho Windows
        try:
            root.tk.call('source', 'azure.tcl')
            root.tk.call('set_theme', 'light')
        except:
            # Fallback nếu không có theme
            pass
        
        # Tạo GUI
        app = MainGUI(root)
        
        logger.info("Ứng dụng đã khởi động thành công")
        
        # Chạy main loop
        root.mainloop()
        
    except Exception as e:
        logger.error(f"Lỗi khởi động ứng dụng: {str(e)}")
        messagebox.showerror("Lỗi", f"Không thể khởi động ứng dụng:\n{str(e)}")
    
    logger.info("Ứng dụng đã thoát")

if __name__ == "__main__":
    main()
