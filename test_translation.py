"""
Script test chức năng dịch tên file cải tiến
"""

import logging
from gemini_translator import GeminiTranslator

def test_translation_with_normalized_names():
    """
    Test dịch tên file đã được chuẩn hóa
    """
    print("=== Test Translation với tên file đã chuẩn hóa ===")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        translator = GeminiTranslator()
        
        # Test connection
        if not translator.test_connection():
            print("✗ Không thể kết nối Gemini API")
            return
        
        print("✓ Kết nối Gemini API thành công\n")
        
        # Test cases với tên file đã chuẩn hóa
        test_cases = [
            "tong-quan-ve-slots-taya365",
            "bai-bac-phat-tai",
            "poker-game-thu-vi", 
            "rong-ho-may-man",
            "casino-online-uy-tin",
            "slot-machine-jackpot",
            "game-bai-doi-thuong",
            "xoc-dia-online"
        ]
        
        print("Đang test dịch các tên file đã chuẩn hóa:")
        print("-" * 60)
        
        for test_case in test_cases:
            print(f"\nTên gốc: '{test_case}'")
            translated = translator.translate_filename(test_case)
            
            if translated:
                print(f"Kết quả: '{translated}'")
                print("✓ Thành công")
            else:
                print("✗ Thất bại")
            
            print("-" * 40)
        
        # Test với tên file có dấu
        print("\n\nĐang test dịch các tên file có dấu:")
        print("-" * 60)
        
        vietnamese_cases = [
            "Hình ảnh casino đẹp",
            "Bài bạc phát tài", 
            "Poker game thú vị"
        ]
        
        for test_case in vietnamese_cases:
            print(f"\nTên gốc: '{test_case}'")
            translated = translator.translate_filename(test_case)
            
            if translated:
                print(f"Kết quả: '{translated}'")
                print("✓ Thành công")
            else:
                print("✗ Thất bại")
            
            print("-" * 40)
                
    except Exception as e:
        print(f"✗ Lỗi: {str(e)}")

def main():
    """
    Chạy test
    """
    print("Bắt đầu test chức năng dịch cải tiến...\n")
    test_translation_with_normalized_names()
    print("\n=== Test hoàn thành ===")

if __name__ == "__main__":
    main()
