"""
Script test chức năng cache translation
"""

import logging
import json
from gemini_translator import GeminiTranslator

def test_cache_functionality():
    """
    Test chức năng cache
    """
    print("=== Test Cache Functionality ===")
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    
    try:
        translator = GeminiTranslator()
        
        # Test với một tên file đơn giản
        test_name = "bai-bac-may-man"
        
        print(f"\nTest 1: Dịch lần đầu - '{test_name}'")
        result1 = translator.translate_filename(test_name)
        print(f"Kết quả: {result1}")
        
        print(f"\nTest 2: Dịch lần thứ hai (should use cache) - '{test_name}'")
        result2 = translator.translate_filename(test_name)
        print(f"Kết quả: {result2}")
        
        print(f"\nKiểm tra cache file...")
        try:
            with open("translation_cache.json", 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
                print(f"Cache hiện có {len(cache_data)} entries:")
                for key, value in cache_data.items():
                    print(f"  '{key}' -> '{value}'")
        except FileNotFoundError:
            print("Cache file không tồn tại")
        except Exception as e:
            print(f"Lỗi đọc cache: {e}")
        
        # Test với tên file khác
        test_names = [
            "slot-machine-jackpot",
            "poker-game-online"
        ]
        
        for name in test_names:
            print(f"\nTest với: '{name}'")
            result = translator.translate_filename(name)
            print(f"Kết quả: {result}")
                
    except Exception as e:
        print(f"✗ Lỗi: {str(e)}")

def main():
    """
    Chạy test
    """
    print("Bắt đầu test cache functionality...\n")
    test_cache_functionality()
    print("\n=== Test hoàn thành ===")

if __name__ == "__main__":
    main()
