# Quản lý Quota Gemini API

## Vấn đề Quota Exceeded

Khi sử dụng Gemini API miễn phí, bạn có thể gặp lỗi "QUOTA_EXCEEDED". <PERSON><PERSON><PERSON> là các cách xử lý:

## Gi<PERSON>i pháp ngay lập tức

### 1. Sử dụng Cache
Ứng dụng đã tích hợp cache để tránh dịch lại:
- C<PERSON> được lưu trong file `translation_cache.json`
- Tự động sử dụng kết quả đã dịch trước đó
- Giảm đáng kể số lượng API calls

### 2. Giảm Batch Size
Trong file `config.py`:
```python
BATCH_SIZE = 1  # Giảm xuống 1 để xử lý từng file một
RETRY_DELAY = 5  # Tăng thời gian chờ gi<PERSON>a c<PERSON><PERSON> calls
```

### 3. <PERSON><PERSON> lý theo đợt nhỏ
- <PERSON>ọ<PERSON> <PERSON>t thư mục hơn mỗi lần
- <PERSON><PERSON> lý 5-10 file một lần thay vì hàng trăm file

## Giải pháp dài hạn

### 1. Thiết lập Billing Account
- Truy cập [Google Cloud Console](https://console.cloud.google.com/)
- Thiết lập billing account
- Tăng quota limits

### 2. Sử dụng API Key khác
- Tạo project mới trong Google Cloud
- Tạo API key mới
- Thay đổi trong `config.py`

### 3. Chờ reset quota
- Quota miễn phí thường reset hàng ngày
- Chờ 24 giờ và thử lại

## Tính năng Fallback

Khi API không khả dụng, ứng dụng sẽ:
1. Sử dụng tên file gốc
2. Vẫn chuẩn hóa tên file (loại bỏ dấu, lowercase)
3. Ghi log cảnh báo
4. Tiếp tục xử lý các file khác

## Monitoring Quota

### Kiểm tra quota hiện tại:
1. Truy cập [Google AI Studio](https://aistudio.google.com/)
2. Xem phần "Usage" hoặc "Quota"
3. Theo dõi số requests đã sử dụng

### Log patterns để nhận biết:
- `QUOTA_EXCEEDED`: Đã vượt quota
- `RATE_LIMIT_EXCEEDED`: Gửi requests quá nhanh
- `API_KEY_INVALID`: API key không hợp lệ

## Best Practices

1. **Sử dụng cache hiệu quả**
   - Không xóa file `translation_cache.json`
   - Backup cache khi có nhiều translations

2. **Batch processing thông minh**
   - Xử lý file theo nhóm nhỏ
   - Tạm dừng giữa các batch

3. **Monitor usage**
   - Theo dõi log để biết khi nào gần hết quota
   - Dừng xử lý khi cần thiết

## Troubleshooting

### Lỗi thường gặp:
```
429 Quota exceeded for quota metric 'Generate Content API requests per day'
```

**Giải pháp:**
1. Chờ đến ngày hôm sau
2. Hoặc thiết lập billing
3. Hoặc sử dụng API key khác

### Cache không hoạt động:
1. Kiểm tra file `translation_cache.json` có tồn tại không
2. Kiểm tra quyền ghi file
3. Xem log có lỗi cache không

### API calls quá nhiều:
1. Giảm `BATCH_SIZE` trong config
2. Tăng `RETRY_DELAY`
3. Xử lý ít file hơn mỗi lần
