"""
Cấu hình cho ứng dụng Image Converter
"""

# Gemini API Configuration
# Vui lòng thay thế bằng API key thực của bạn từ https://aistudio.google.com/
GEMINI_API_KEY = "AIzaSyD_VAwV_YhJL8QkGEr_Mbko5dxM72aVLBg"  # Thay thế bằng API key thực
GEMINI_MODEL = "gemini-2.0-flash"  # Sử dụng model ổn định hơn

# Image Processing Settings
WEBP_QUALITY = 85  # Chất lượng WebP (0-100)
SUPPORTED_FORMATS = ['.jpg', '.jpeg', '.JPG', '.JPEG']

# File Processing Settings
MAX_RETRIES = 3  # Số lần thử lại khi API lỗi
RETRY_DELAY = 2  # Thời gian chờ giữa các lần thử lại (giây) - tăng để tránh quota
BATCH_SIZE = 2  # Số file xử lý đồng thời - giảm để tránh quota
ENABLE_TRANSLATION_CACHE = True  # Bật cache để tránh dịch lại

# GUI Settings
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
LOG_MAX_LINES = 1000

# Translation Prompt Templates
TRANSLATION_PROMPT = """
Tên file này có thể là tiếng Việt có dấu hoặc không dấu (đã được chuẩn hóa).
Hãy đọc hiểu nghĩa của tên file và dịch sang tiếng Anh theo ngữ cảnh Casino/Gaming.

Ví dụ:
- "tong-quan-ve-slots" -> "slots-overview"
- "bai-bac-phat-tai" -> "lucky-gambling"
- "poker-game-thu-vi" -> "exciting-poker-game"

Chỉ trả về tên đã dịch, không giải thích thêm.
Tên file: "{filename}"
"""

# Prompt để phục hồi dấu tiếng Việt
RESTORE_VIETNAMESE_PROMPT = """
Tên file này là tiếng Việt không dấu đã được chuẩn hóa (dấu gạch ngang thay khoảng trắng).
Hãy phục hồi lại dấu tiếng Việt để dễ hiểu nghĩa hơn.

Ví dụ:
- "tong-quan-ve-slots" -> "tổng quan về slots"
- "bai-bac-phat-tai" -> "bài bạc phát tài"
- "poker-game-thu-vi" -> "poker game thú vị"

Chỉ trả về tên đã phục hồi dấu, không giải thích thêm.
Tên file: "{filename}"
"""
