"""
Module tích hợp Gemini API để dịch tên file
"""

import google.generativeai as genai
import time
import logging
import re
import json
import os
from config import GEMINI_API_KEY, GEMINI_MODEL, MAX_RETRIES, RETRY_DELAY, TRANSLATION_PROMPT, RESTORE_VIETNAMESE_PROMPT, ENABLE_TRANSLATION_CACHE

class GeminiTranslator:
    def __init__(self):
        """
        Khởi tạo GeminiTranslator
        """
        self.logger = logging.getLogger(__name__)
        self.model = None
        self.cache_file = "translation_cache.json"
        self.translation_cache = {}
        self._load_cache()
        self._initialize_api()
    
    def _load_cache(self):
        """
        Tải cache từ file
        """
        if ENABLE_TRANSLATION_CACHE and os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    self.translation_cache = json.load(f)
                self.logger.info(f"Đã tải {len(self.translation_cache)} bản dịch từ cache")
            except Exception as e:
                self.logger.warning(f"Không thể tải cache: {str(e)}")
                self.translation_cache = {}

    def _save_cache(self):
        """
        Lưu cache vào file
        """
        if ENABLE_TRANSLATION_CACHE:
            try:
                with open(self.cache_file, 'w', encoding='utf-8') as f:
                    json.dump(self.translation_cache, f, ensure_ascii=False, indent=2)
                self.logger.debug("Đã lưu cache")
            except Exception as e:
                self.logger.warning(f"Không thể lưu cache: {str(e)}")

    def _get_from_cache(self, key):
        """
        Lấy bản dịch từ cache
        """
        if ENABLE_TRANSLATION_CACHE and key in self.translation_cache:
            self.logger.info(f"Sử dụng cache cho: '{key}'")
            return self.translation_cache[key]
        return None

    def _save_to_cache(self, key, value):
        """
        Lưu bản dịch vào cache
        """
        if ENABLE_TRANSLATION_CACHE:
            self.translation_cache[key] = value
            self._save_cache()

    def _initialize_api(self):
        """
        Khởi tạo Gemini API
        """
        try:
            genai.configure(api_key=GEMINI_API_KEY)
            self.model = genai.GenerativeModel(GEMINI_MODEL)
            self.logger.info("Đã khởi tạo Gemini API thành công")
        except Exception as e:
            self.logger.error(f"Lỗi khởi tạo Gemini API: {str(e)}")
            raise
    
    def _is_normalized_vietnamese(self, text):
        """
        Kiểm tra xem text có phải là tiếng Việt đã được chuẩn hóa không
        (không dấu, có dấu gạch ngang)

        Args:
            text (str): Text cần kiểm tra

        Returns:
            bool: True nếu là tiếng Việt đã chuẩn hóa
        """
        # Kiểm tra có dấu gạch ngang và không có dấu tiếng Việt
        has_hyphens = '-' in text
        has_vietnamese_chars = bool(re.search(r'[àáạảãâầấậẩẫăằắặẳẵèéẹẻẽêềếệểễìíịỉĩòóọỏõôồốộổỗơờớợởỡùúụủũưừứựửữỳýỵỷỹđ]', text.lower()))

        # Nếu có dấu gạch ngang và không có dấu tiếng Việt, có thể là đã chuẩn hóa
        return has_hyphens and not has_vietnamese_chars

    def _restore_vietnamese_accents(self, normalized_name):
        """
        Phục hồi dấu tiếng Việt cho tên file đã được chuẩn hóa

        Args:
            normalized_name (str): Tên file đã chuẩn hóa

        Returns:
            str: Tên file có dấu tiếng Việt hoặc None nếu lỗi
        """
        if not self.model:
            return None

        prompt = RESTORE_VIETNAMESE_PROMPT.format(filename=normalized_name)

        for attempt in range(MAX_RETRIES):
            try:
                self.logger.info(f"Đang phục hồi dấu: '{normalized_name}' (lần thử {attempt + 1})")

                response = self.model.generate_content(prompt)

                if response and response.text:
                    restored = response.text.strip()
                    self.logger.info(f"Phục hồi thành công: '{normalized_name}' -> '{restored}'")
                    return restored
                else:
                    self.logger.warning(f"Không nhận được phản hồi từ API (lần thử {attempt + 1})")

            except Exception as e:
                self.logger.error(f"Lỗi API khi phục hồi dấu (lần thử {attempt + 1}): {str(e)}")

                if attempt < MAX_RETRIES - 1:
                    self.logger.info(f"Chờ {RETRY_DELAY} giây trước khi thử lại...")
                    time.sleep(RETRY_DELAY)

        self.logger.error(f"Không thể phục hồi dấu cho '{normalized_name}' sau {MAX_RETRIES} lần thử")
        return None

    def translate_filename(self, vietnamese_name):
        """
        Dịch tên file từ tiếng Việt sang tiếng Anh

        Args:
            vietnamese_name (str): Tên file tiếng Việt (có thể có hoặc không có dấu)

        Returns:
            str: Tên file tiếng Anh hoặc None nếu lỗi
        """
        # Kiểm tra cache trước
        cached_result = self._get_from_cache(vietnamese_name)
        if cached_result:
            return cached_result

        if not self.model:
            self.logger.error("Gemini API chưa được khởi tạo")
            return None

        # Kiểm tra xem có phải tên file đã được chuẩn hóa không
        name_to_translate = vietnamese_name

        if self._is_normalized_vietnamese(vietnamese_name):
            self.logger.info(f"Phát hiện tên file đã chuẩn hóa: '{vietnamese_name}'")

            # Kiểm tra cache cho tên đã phục hồi
            restore_cache_key = f"restore_{vietnamese_name}"
            restored_name = self._get_from_cache(restore_cache_key)

            if not restored_name:
                # Thử phục hồi dấu tiếng Việt trước
                restored_name = self._restore_vietnamese_accents(vietnamese_name)
                if restored_name:
                    self._save_to_cache(restore_cache_key, restored_name)

            if restored_name:
                name_to_translate = restored_name
            else:
                # Nếu không phục hồi được, vẫn dùng tên gốc
                self.logger.warning(f"Không thể phục hồi dấu, sử dụng tên gốc: '{vietnamese_name}'")

        # Dịch sang tiếng Anh
        prompt = TRANSLATION_PROMPT.format(filename=name_to_translate)

        for attempt in range(MAX_RETRIES):
            try:
                self.logger.info(f"Đang dịch: '{name_to_translate}' (lần thử {attempt + 1})")

                response = self.model.generate_content(prompt)

                if response and response.text:
                    translated = response.text.strip()
                    self.logger.info(f"Dịch thành công: '{name_to_translate}' -> '{translated}'")

                    # Lưu vào cache
                    self._save_to_cache(vietnamese_name, translated)

                    return translated
                else:
                    self.logger.warning(f"Không nhận được phản hồi từ API (lần thử {attempt + 1})")

            except Exception as e:
                error_msg = str(e)
                if "QUOTA_EXCEEDED" in error_msg or "quota" in error_msg.lower():
                    self.logger.error("Đã vượt quá quota API. Vui lòng chờ hoặc kiểm tra billing account.")
                    break  # Không thử lại nếu vượt quota
                else:
                    self.logger.error(f"Lỗi API (lần thử {attempt + 1}): {error_msg}")

                if attempt < MAX_RETRIES - 1:
                    self.logger.info(f"Chờ {RETRY_DELAY} giây trước khi thử lại...")
                    time.sleep(RETRY_DELAY)

        self.logger.error(f"Không thể dịch '{name_to_translate}' sau {MAX_RETRIES} lần thử")
        return None
    
    def translate_batch(self, filenames):
        """
        Dịch nhiều tên file cùng lúc
        
        Args:
            filenames (list): Danh sách tên file tiếng Việt
            
        Returns:
            dict: Dictionary mapping tên gốc -> tên đã dịch
        """
        results = {}
        
        for filename in filenames:
            translated = self.translate_filename(filename)
            if translated:
                results[filename] = translated
            else:
                # Fallback: giữ nguyên tên gốc nếu không dịch được
                results[filename] = filename
                self.logger.warning(f"Sử dụng tên gốc cho: {filename}")
        
        return results
    
    def test_connection(self):
        """
        Test kết nối với Gemini API

        Returns:
            bool: True nếu kết nối thành công
        """
        try:
            test_response = self.model.generate_content("Hello")
            return test_response and test_response.text
        except Exception as e:
            error_msg = str(e)
            if "API_KEY_INVALID" in error_msg:
                self.logger.error("API key không hợp lệ. Vui lòng kiểm tra lại GEMINI_API_KEY trong config.py")
            elif "QUOTA_EXCEEDED" in error_msg:
                self.logger.error("Đã vượt quá quota API. Vui lòng kiểm tra billing account.")
            else:
                self.logger.error(f"Test kết nối thất bại: {error_msg}")
            return False
