"""
Module tích hợp Gemini API để dịch tên file
"""

import google.generativeai as genai
import time
import logging
from config import GEMINI_API_KEY, GEMINI_MODEL, MAX_RETRIES, RETRY_DELAY, TRANSLATION_PROMPT

class GeminiTranslator:
    def __init__(self):
        """
        Khởi tạo GeminiTranslator
        """
        self.logger = logging.getLogger(__name__)
        self.model = None
        self._initialize_api()
    
    def _initialize_api(self):
        """
        Khởi tạo Gemini API
        """
        try:
            genai.configure(api_key=GEMINI_API_KEY)
            self.model = genai.GenerativeModel(GEMINI_MODEL)
            self.logger.info("Đã khởi tạo Gemini API thành công")
        except Exception as e:
            self.logger.error(f"Lỗi khởi tạo Gemini API: {str(e)}")
            raise
    
    def translate_filename(self, vietnamese_name):
        """
        Dịch tên file từ tiếng <PERSON> sang tiếng Anh
        
        Args:
            vietnamese_name (str): Tên file tiếng Vi<PERSON>t (không có extension)
            
        Returns:
            str: Tên file tiếng Anh hoặc None nếu lỗi
        """
        if not self.model:
            self.logger.error("Gemini API chưa được khởi tạo")
            return None
        
        prompt = TRANSLATION_PROMPT.format(filename=vietnamese_name)
        
        for attempt in range(MAX_RETRIES):
            try:
                self.logger.info(f"Đang dịch: '{vietnamese_name}' (lần thử {attempt + 1})")
                
                response = self.model.generate_content(prompt)
                
                if response and response.text:
                    translated = response.text.strip()
                    self.logger.info(f"Dịch thành công: '{vietnamese_name}' -> '{translated}'")
                    return translated
                else:
                    self.logger.warning(f"Không nhận được phản hồi từ API (lần thử {attempt + 1})")
                    
            except Exception as e:
                self.logger.error(f"Lỗi API (lần thử {attempt + 1}): {str(e)}")
                
                if attempt < MAX_RETRIES - 1:
                    self.logger.info(f"Chờ {RETRY_DELAY} giây trước khi thử lại...")
                    time.sleep(RETRY_DELAY)
        
        self.logger.error(f"Không thể dịch '{vietnamese_name}' sau {MAX_RETRIES} lần thử")
        return None
    
    def translate_batch(self, filenames):
        """
        Dịch nhiều tên file cùng lúc
        
        Args:
            filenames (list): Danh sách tên file tiếng Việt
            
        Returns:
            dict: Dictionary mapping tên gốc -> tên đã dịch
        """
        results = {}
        
        for filename in filenames:
            translated = self.translate_filename(filename)
            if translated:
                results[filename] = translated
            else:
                # Fallback: giữ nguyên tên gốc nếu không dịch được
                results[filename] = filename
                self.logger.warning(f"Sử dụng tên gốc cho: {filename}")
        
        return results
    
    def test_connection(self):
        """
        Test kết nối với Gemini API

        Returns:
            bool: True nếu kết nối thành công
        """
        try:
            test_response = self.model.generate_content("Hello")
            return test_response and test_response.text
        except Exception as e:
            error_msg = str(e)
            if "API_KEY_INVALID" in error_msg:
                self.logger.error("API key không hợp lệ. Vui lòng kiểm tra lại GEMINI_API_KEY trong config.py")
            elif "QUOTA_EXCEEDED" in error_msg:
                self.logger.error("Đã vượt quá quota API. Vui lòng kiểm tra billing account.")
            else:
                self.logger.error(f"Test kết nối thất bại: {error_msg}")
            return False
