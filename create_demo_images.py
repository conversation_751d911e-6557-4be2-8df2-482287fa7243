"""
Script tạo hình ảnh demo để test ứng dụng
"""

import os
from PIL import Image, ImageDraw, ImageFont

def create_demo_images():
    """
    Tạo một số hình ảnh demo với tên tiếng Việt
    """
    # T<PERSON><PERSON> thư mục demo
    demo_folder = "demo_images"
    if not os.path.exists(demo_folder):
        os.makedirs(demo_folder)
    
    # <PERSON>h sách tên file demo
    demo_names = [
        "Hình ảnh casino đẹp",
        "Bài bạc phát tài",
        "Slot machine jackpot",
        "Poker game thú vị",
        "Rồng hổ may mắn",
        "Baccarat VIP room"
    ]
    
    # Tạo hình ảnh
    for i, name in enumerate(demo_names):
        # Tạo hình ảnh 400x300 với màu ngẫu nhiên
        img = Image.new('RGB', (400, 300), color=(
            (i * 50) % 255,
            (i * 80) % 255, 
            (i * 120) % 255
        ))
        
        # Vẽ text lên hình
        draw = ImageDraw.Draw(img)
        
        try:
            # Thử sử dụng font hệ thống
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # Fallback font
            font = ImageFont.load_default()
        
        # Vẽ tên file lên hình
        text_bbox = draw.textbbox((0, 0), name, font=font)
        text_width = text_bbox[2] - text_bbox[0]
        text_height = text_bbox[3] - text_bbox[1]
        
        x = (400 - text_width) // 2
        y = (300 - text_height) // 2
        
        draw.text((x, y), name, fill='white', font=font)
        
        # Lưu file
        filename = f"{name}.jpg"
        filepath = os.path.join(demo_folder, filename)
        img.save(filepath, 'JPEG', quality=90)
        
        print(f"Đã tạo: {filepath}")
    
    print(f"\nĐã tạo {len(demo_names)} hình ảnh demo trong thư mục '{demo_folder}'")
    print("Bạn có thể sử dụng thư mục này để test ứng dụng.")

if __name__ == "__main__":
    create_demo_images()
